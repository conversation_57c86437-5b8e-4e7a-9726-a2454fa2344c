@import "tailwindcss";

/* Custom CSS untuk Personal Portfolio <PERSON> */

/* Navigation Link Hover Effects */
.nav-link {
  position: relative;
  transition: all 0.3s ease;
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: -4px;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #181818, #6b7280);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 50%;
}

[data-theme="dark"] .nav-link::after {
  background: linear-gradient(90deg, #ffffff, #f3f4f6);
}

/* Mobile Navigation Link Hover Effects */
.nav-link-mobile {
  position: relative;
  transition: all 0.3s ease;
  padding-left: 0;
}

.nav-link-mobile::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #181818, #6b7280);
  transition: all 0.3s ease;
  transform: translateY(-50%);
}

.nav-link-mobile:hover::before {
  width: 20px;
}

[data-theme="dark"] .nav-link-mobile::before {
  background: linear-gradient(90deg, #ffffff, #f3f4f6);
}

/* Skills Filter Button Styles */
.skill-filter-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 9999px;
  border: 2px solid #181818;
  color: #181818;
  font-weight: 500;
  transition: all 0.3s ease;
}

.skill-filter-btn:hover {
  background-color: #181818;
  color: #ffffff;
}

.skill-filter-btn.active {
  background-color: #181818;
  color: #ffffff;
}

[data-theme="dark"] .skill-filter-btn {
  border-color: #ffffff;
  color: #ffffff;
}

[data-theme="dark"] .skill-filter-btn:hover {
  background-color: #ffffff;
  color: #181818;
}

[data-theme="dark"] .skill-filter-btn.active {
  background-color: #ffffff;
  color: #181818;
}

/* Card Hover Effects */
.card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card:hover {
  transform: translateY(-8px);
}

/* Social Icon Hover Effects */
.social-icon {
  transition: all 0.3s ease;
}

.social-icon:hover {
  transform: translateY(-2px);
}

/* Button Hover Effects */
.btn-primary,
.btn-secondary {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-primary::before,
.btn-secondary::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.btn-primary:hover::before,
.btn-secondary:hover::before {
  left: 100%;
}

/* Loading Animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Scroll Animations */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.animate-on-scroll.in-view {
  opacity: 1;
  transform: translateY(0);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

[data-theme="dark"] ::-webkit-scrollbar-track {
  background: #2d2d2d;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: #666;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: #888;
}

/* Text Selection */
::selection {
  background: rgba(24, 24, 24, 0.2);
  color: #181818;
}

[data-theme="dark"] ::selection {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

/* Focus Styles */
button:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid #181818;
  outline-offset: 2px;
}

[data-theme="dark"] button:focus,
[data-theme="dark"] input:focus,
[data-theme="dark"] textarea:focus,
[data-theme="dark"] select:focus {
  outline-color: #ffffff;
}

/* Notification Styles */
.notification {
  max-width: 400px;
  min-width: 300px;
}

.notification-success {
  border-left: 4px solid #10b981;
}

.notification-warning {
  border-left: 4px solid #f59e0b;
}

.notification-error {
  border-left: 4px solid #ef4444;
}

.notification-info {
  border-left: 4px solid #3b82f6;
}

/* Responsive Typography */
@media (max-width: 640px) {
  .prose p {
    font-size: 0.9rem;
    line-height: 1.6;
  }
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }
}
