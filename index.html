<!doctype html>
<html lang="id" class="scroll-smooth" data-theme="light">
  <head>
    <!-- Meta Tags Dasar -->
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />

    <!-- SEO Meta Tags -->
    <title>
      <PERSON> - AI Prompt Engineer | Cyber Security | UI
      Designer
    </title>
    <meta
      name="description"
      content="Personal Portfolio Muhammad <PERSON> - AI Prompt Engineer, Cyber Security Specialist, dan U<PERSON> Designer. <PERSON><PERSON> proyek, skill, dan sertifikat terbaru."
    />
    <meta
      name="keywords"
      content="<PERSON>, AI Prompt Engineer, Cyber Security, UI Designer, Portfolio, Web Developer"
    />
    <meta name="author" content="Muhammad Sya<PERSON>l Muzhaffar" />
    <meta name="robots" content="index, follow" />
    <meta name="language" content="Indonesian" />

    <!-- Open Graph Meta Tags -->
    <meta
      property="og:title"
      content="Muhammad <PERSON> - AI Prompt Engineer | Cyber Security | UI Designer"
    />
    <meta
      property="og:description"
      content="Personal Portfolio Muhammad Syaamil Muzhaffar - AI Prompt Engineer, Cyber Security Specialist, dan UI Designer."
    />
    <meta property="og:type" content="website" />
    <meta
      property="og:url"
      content="https://muhammadsyaamilmuzhaffar.github.io/personal-portfolio"
    />
    <meta
      property="og:image"
      content="./assets/images/profile/muhammadsyaamilmuzhaffar.png"
    />
    <meta
      property="og:site_name"
      content="Muhammad Syaamil Muzhaffar Portfolio"
    />
    <meta property="og:locale" content="id_ID" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta
      name="twitter:title"
      content="Muhammad Syaamil Muzhaffar - AI Prompt Engineer | Cyber Security | UI Designer"
    />
    <meta
      name="twitter:description"
      content="Personal Portfolio Muhammad Syaamil Muzhaffar - AI Prompt Engineer, Cyber Security Specialist, dan UI Designer."
    />
    <meta
      name="twitter:image"
      content="./assets/images/profile/muhammadsyaamilmuzhaffar.png"
    />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="./assets/icons/favicon.ico" />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="./assets/icons/apple-touch-icon.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="./assets/icons/favicon-32x32.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="./assets/icons/favicon-16x16.png"
    />

    <!-- Preconnect untuk Performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Google Fonts -->
    <link
      href="https://fonts.googleapis.com/css2?family=Merriweather:wght@400;700&family=Open+Sans:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Stylesheet -->
    <link href="./dist/css/output.css" rel="stylesheet" />

    <!-- Structured Data -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Person",
        "name": "Muhammad Syaamil Muzhaffar",
        "jobTitle": [
          "AI Prompt Engineer",
          "Cyber Security Specialist",
          "UI Designer"
        ],
        "description": "AI Prompt Engineer, Cyber Security Specialist, dan UI Designer dengan pengalaman dalam pengembangan web dan keamanan siber.",
        "url": "https://muhammadsyaamilmuzhaffar.github.io/personal-portfolio",
        "image": "./assets/images/profile/muhammadsyaamilmuzhaffar.png",
        "sameAs": [
          "https://github.com/muhammadsyaamilmuzhaffar",
          "https://linkedin.com/in/muhammadsyaamilmuzhaffar",
          "https://instagram.com/muhammadsyaamilmuzhaffar"
        ]
      }
    </script>
  </head>

  <body
    class="font-body bg-primary-light dark:bg-primary-dark text-primary-dark dark:text-primary-light transition-colors duration-300 no-select"
  >
    <!-- Loading Screen -->
    <div
      id="loading-screen"
      class="fixed inset-0 bg-primary-light dark:bg-primary-dark z-overlay flex items-center justify-center"
    >
      <div class="text-center">
        <div
          class="animate-spin rounded-full h-16 w-16 border-4 border-primary-dark dark:border-primary-light border-t-transparent mx-auto mb-4"
        ></div>
        <p class="text-lg font-medium">Memuat...</p>
      </div>
    </div>

    <!-- Navigation Bar -->
    <nav
      id="navbar"
      class="fixed top-0 left-0 right-0 z-navbar bg-primary-light/90 dark:bg-primary-dark/90 backdrop-blur-navbar border-b border-gray-default/20 transition-all duration-300"
    >
      <div class="container mx-auto px-6 lg:px-8">
        <div class="flex items-center justify-between h-16 lg:h-20">
          <!-- Logo/Brand -->
          <div class="flex-shrink-0">
            <button
              id="brand-logo"
              class="text-xl lg:text-2xl font-heading font-heading-bold text-primary-dark dark:text-primary-light hover:text-gray-dark dark:hover:text-gray-light transition-colors duration-300"
            >
              Muhammad Syaamil Muzhaffar
            </button>
          </div>

          <!-- Desktop Navigation -->
          <div class="hidden lg:flex items-center space-x-8">
            <a
              href="#about"
              class="nav-link text-primary-dark dark:text-primary-light hover:text-gray-dark dark:hover:text-gray-light transition-all duration-300 relative"
            >
              Tentang Saya
            </a>
            <a
              href="#skills"
              class="nav-link text-primary-dark dark:text-primary-light hover:text-gray-dark dark:hover:text-gray-light transition-all duration-300 relative"
            >
              Skill
            </a>
            <a
              href="#projects"
              class="nav-link text-primary-dark dark:text-primary-light hover:text-gray-dark dark:hover:text-gray-light transition-all duration-300 relative"
            >
              Proyek
            </a>
            <a
              href="#certificates"
              class="nav-link text-primary-dark dark:text-primary-light hover:text-gray-dark dark:hover:text-gray-light transition-all duration-300 relative"
            >
              Sertifikat
            </a>
            <a
              href="#tech-stack"
              class="nav-link text-primary-dark dark:text-primary-light hover:text-gray-dark dark:hover:text-gray-light transition-all duration-300 relative"
            >
              Tech Stack
            </a>
            <a
              href="#contact"
              class="nav-link text-primary-dark dark:text-primary-light hover:text-gray-dark dark:hover:text-gray-light transition-all duration-300 relative"
            >
              Kontak
            </a>
          </div>

          <!-- Desktop Theme Toggle -->
          <div class="hidden lg:flex items-center space-x-4">
            <button
              id="theme-toggle-desktop"
              class="p-2 rounded-full bg-gray-light dark:bg-gray-dark hover:bg-gray-default/20 dark:hover:bg-gray-light/20 transition-all duration-300"
              aria-label="Toggle theme"
            >
              <svg
                id="sun-icon"
                class="w-5 h-5 text-primary-dark dark:text-primary-light hidden dark:block"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
                  clip-rule="evenodd"
                ></path>
              </svg>
              <svg
                id="moon-icon"
                class="w-5 h-5 text-primary-dark dark:text-primary-light block dark:hidden"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"
                ></path>
              </svg>
            </button>
          </div>

          <!-- Mobile Menu Button -->
          <div class="lg:hidden flex items-center space-x-4">
            <!-- Mobile Theme Toggle -->
            <label class="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                id="theme-toggle-mobile"
                class="sr-only peer"
              />
              <div
                class="w-11 h-6 bg-gray-light peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-dark/20 dark:peer-focus:ring-primary-light/20 rounded-full peer dark:bg-gray-dark peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-dark dark:peer-checked:bg-primary-light"
              ></div>
            </label>

            <!-- Hamburger Menu -->
            <button
              id="mobile-menu-toggle"
              class="p-2 rounded-md text-primary-dark dark:text-primary-light hover:bg-gray-light dark:hover:bg-gray-dark transition-colors duration-300"
              aria-label="Toggle mobile menu"
            >
              <svg
                id="hamburger-icon"
                class="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 6h16M4 12h16M4 18h16"
                ></path>
              </svg>
              <svg
                id="close-icon"
                class="w-6 h-6 hidden"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M6 18L18 6M6 6l12 12"
                ></path>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- Mobile Navigation Menu -->
      <div
        id="mobile-menu"
        class="lg:hidden hidden bg-primary-light dark:bg-primary-dark border-t border-gray-default/20"
      >
        <div class="px-6 py-4 space-y-4">
          <a
            href="#about"
            class="block nav-link-mobile text-primary-dark dark:text-primary-light hover:text-gray-dark dark:hover:text-gray-light transition-colors duration-300"
          >
            Tentang Saya
          </a>
          <a
            href="#skills"
            class="block nav-link-mobile text-primary-dark dark:text-primary-light hover:text-gray-dark dark:hover:text-gray-light transition-colors duration-300"
          >
            Skill
          </a>
          <a
            href="#projects"
            class="block nav-link-mobile text-primary-dark dark:text-primary-light hover:text-gray-dark dark:hover:text-gray-light transition-colors duration-300"
          >
            Proyek
          </a>
          <a
            href="#certificates"
            class="block nav-link-mobile text-primary-dark dark:text-primary-light hover:text-gray-dark dark:hover:text-gray-light transition-colors duration-300"
          >
            Sertifikat
          </a>
          <a
            href="#tech-stack"
            class="block nav-link-mobile text-primary-dark dark:text-primary-light hover:text-gray-dark dark:hover:text-gray-light transition-colors duration-300"
          >
            Tech Stack
          </a>
          <a
            href="#contact"
            class="block nav-link-mobile text-primary-dark dark:text-primary-light hover:text-gray-dark dark:hover:text-gray-light transition-colors duration-300"
          >
            Kontak
          </a>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="pt-16 lg:pt-20">
      <!-- Hero Section -->
      <section
        id="hero"
        class="min-h-screen flex items-center justify-center px-6 lg:px-8 py-20"
      >
        <div class="container mx-auto">
          <div
            class="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center"
          >
            <!-- Hero Text Content -->
            <div class="text-center lg:text-left space-y-6 animate-slide-up">
              <div class="space-y-4">
                <p
                  class="text-lg lg:text-xl text-gray-default font-body-regular"
                >
                  Halo Semua 👋, Saya
                </p>
                <h1
                  class="text-4xl lg:text-5xl xl:text-6xl font-heading font-heading-bold text-primary-dark dark:text-primary-light leading-heading"
                >
                  Muhammad Syaamil Muzhaffar
                </h1>
                <h2
                  class="text-xl lg:text-2xl xl:text-3xl font-body font-body-semibold text-gray-dark dark:text-gray-light"
                >
                  AI Prompt Engineer | Cyber Security | UI Designer
                </h2>
                <p
                  class="text-lg lg:text-xl text-gray-default font-body-regular italic"
                >
                  "Coba dulu, coba dulu"
                </p>
              </div>

              <!-- CTA Button -->
              <div class="pt-6">
                <a
                  href="#contact"
                  class="btn-primary inline-flex items-center space-x-2 hover:scale-105 transform transition-all duration-300"
                >
                  <span>Hubungi Saya</span>
                  <svg
                    class="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M17 8l4 4m0 0l-4 4m4-4H3"
                    ></path>
                  </svg>
                </a>
              </div>
            </div>

            <!-- Hero Image -->
            <div class="flex justify-center lg:justify-end animate-slide-left">
              <div class="relative">
                <div
                  class="w-80 h-80 lg:w-96 lg:h-96 rounded-full overflow-hidden border-4 border-primary-dark dark:border-primary-light shadow-glow-light dark:shadow-glow-dark animate-float"
                >
                  <img
                    src="./assets/images/profile/muhammadsyaamilmuzhaffar.png"
                    alt="Muhammad Syaamil Muzhaffar - AI Prompt Engineer, Cyber Security, UI Designer"
                    class="w-full h-full object-cover"
                    loading="eager"
                  />
                </div>
                <!-- Decorative Elements -->
                <div
                  class="absolute -top-4 -right-4 w-8 h-8 bg-primary-dark dark:bg-primary-light rounded-full animate-pulse-gentle"
                ></div>
                <div
                  class="absolute -bottom-4 -left-4 w-6 h-6 bg-gray-default rounded-full animate-bounce-gentle"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- About Section -->
      <section
        id="about"
        class="py-20 px-6 lg:px-8 bg-gray-light/50 dark:bg-gray-dark/20"
      >
        <div class="container mx-auto">
          <div
            class="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-start"
          >
            <!-- About Content -->
            <div class="space-y-6 animate-slide-right">
              <div class="space-y-4">
                <h2
                  class="text-3xl lg:text-4xl font-heading font-heading-bold text-primary-dark dark:text-primary-light"
                >
                  Tentang Saya
                </h2>
                <h3
                  class="text-xl lg:text-2xl font-body font-body-semibold text-gray-dark dark:text-gray-light"
                >
                  Siapa saya?
                </h3>
                <div
                  class="prose prose-lg text-gray-default font-body-regular leading-body"
                >
                  <p>
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed
                    do eiusmod tempor incididunt ut labore et dolore magna
                    aliqua. Ut enim ad minim veniam, quis nostrud exercitation
                    ullamco laboris nisi ut aliquip ex ea commodo consequat.
                  </p>
                  <p>
                    Duis aute irure dolor in reprehenderit in voluptate velit
                    esse cillum dolore eu fugiat nulla pariatur. Excepteur sint
                    occaecat cupidatat non proident, sunt in culpa qui officia
                    deserunt mollit anim id est laborum.
                  </p>
                  <p>
                    Sed ut perspiciatis unde omnis iste natus error sit
                    voluptatem accusantium doloremque laudantium, totam rem
                    aperiam, eaque ipsa quae ab illo inventore veritatis et
                    quasi architecto beatae vitae dicta sunt explicabo.
                  </p>
                </div>
              </div>
            </div>

            <!-- Social Media Section -->
            <div class="space-y-6 animate-slide-left">
              <div class="space-y-4">
                <h3
                  class="text-xl lg:text-2xl font-body font-body-semibold text-gray-dark dark:text-gray-light"
                >
                  Ingin mengenal saya lebih jauh?
                </h3>
                <p class="text-lg text-gray-default font-body-regular">
                  Kunjungi sosial media saya.
                </p>
              </div>

              <!-- Social Media Icons -->
              <div class="flex flex-wrap gap-4 justify-start">
                <a
                  href="#"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="social-icon group"
                  aria-label="Credly"
                >
                  <div
                    class="w-12 h-12 bg-gray-default hover:bg-primary-dark dark:hover:bg-primary-light rounded-full flex items-center justify-center transition-all duration-300 group-hover:scale-110 group-hover:shadow-button-hover"
                  >
                    <img
                      src="./assets/icons/social-media/credly.svg"
                      alt="Credly"
                      class="w-6 h-6 filter brightness-0 invert group-hover:brightness-100 group-hover:invert-0 transition-all duration-300"
                    />
                  </div>
                </a>
                <a
                  href="#"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="social-icon group"
                  aria-label="GitHub"
                >
                  <div
                    class="w-12 h-12 bg-gray-default hover:bg-primary-dark dark:hover:bg-primary-light rounded-full flex items-center justify-center transition-all duration-300 group-hover:scale-110 group-hover:shadow-button-hover"
                  >
                    <img
                      src="./assets/icons/social-media/github.svg"
                      alt="GitHub"
                      class="w-6 h-6 filter brightness-0 invert group-hover:brightness-100 group-hover:invert-0 transition-all duration-300"
                    />
                  </div>
                </a>
                <a
                  href="#"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="social-icon group"
                  aria-label="Instagram"
                >
                  <div
                    class="w-12 h-12 bg-gray-default hover:bg-primary-dark dark:hover:bg-primary-light rounded-full flex items-center justify-center transition-all duration-300 group-hover:scale-110 group-hover:shadow-button-hover"
                  >
                    <img
                      src="./assets/icons/social-media/instagram.svg"
                      alt="Instagram"
                      class="w-6 h-6 filter brightness-0 invert group-hover:brightness-100 group-hover:invert-0 transition-all duration-300"
                    />
                  </div>
                </a>
                <a
                  href="#"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="social-icon group"
                  aria-label="LinkedIn"
                >
                  <div
                    class="w-12 h-12 bg-gray-default hover:bg-primary-dark dark:hover:bg-primary-light rounded-full flex items-center justify-center transition-all duration-300 group-hover:scale-110 group-hover:shadow-button-hover"
                  >
                    <img
                      src="./assets/icons/social-media/linkedin.svg"
                      alt="LinkedIn"
                      class="w-6 h-6 filter brightness-0 invert group-hover:brightness-100 group-hover:invert-0 transition-all duration-300"
                    />
                  </div>
                </a>
                <a
                  href="#"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="social-icon group"
                  aria-label="Medium"
                >
                  <div
                    class="w-12 h-12 bg-gray-default hover:bg-primary-dark dark:hover:bg-primary-light rounded-full flex items-center justify-center transition-all duration-300 group-hover:scale-110 group-hover:shadow-button-hover"
                  >
                    <img
                      src="./assets/icons/social-media/medium.svg"
                      alt="Medium"
                      class="w-6 h-6 filter brightness-0 invert group-hover:brightness-100 group-hover:invert-0 transition-all duration-300"
                    />
                  </div>
                </a>
                <a
                  href="#"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="social-icon group"
                  aria-label="Spotify"
                >
                  <div
                    class="w-12 h-12 bg-gray-default hover:bg-primary-dark dark:hover:bg-primary-light rounded-full flex items-center justify-center transition-all duration-300 group-hover:scale-110 group-hover:shadow-button-hover"
                  >
                    <img
                      src="./assets/icons/social-media/spotify.svg"
                      alt="Spotify"
                      class="w-6 h-6 filter brightness-0 invert group-hover:brightness-100 group-hover:invert-0 transition-all duration-300"
                    />
                  </div>
                </a>
                <a
                  href="#"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="social-icon group"
                  aria-label="Twitter"
                >
                  <div
                    class="w-12 h-12 bg-gray-default hover:bg-primary-dark dark:hover:bg-primary-light rounded-full flex items-center justify-center transition-all duration-300 group-hover:scale-110 group-hover:shadow-button-hover"
                  >
                    <img
                      src="./assets/icons/social-media/twitter.svg"
                      alt="Twitter"
                      class="w-6 h-6 filter brightness-0 invert group-hover:brightness-100 group-hover:invert-0 transition-all duration-300"
                    />
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Skills Section -->
      <section id="skills" class="py-20 px-6 lg:px-8">
        <div class="container mx-auto">
          <!-- Section Header -->
          <div class="text-center space-y-4 mb-16">
            <h2
              class="text-3xl lg:text-4xl font-heading font-heading-bold text-primary-dark dark:text-primary-light"
            >
              Skill
            </h2>
            <h3
              class="text-xl lg:text-2xl font-body font-body-semibold text-gray-dark dark:text-gray-light"
            >
              Keahlian
            </h3>
            <p
              class="text-lg text-gray-default font-body-regular max-w-2xl mx-auto"
            >
              Kemampuan yang saya miliki
            </p>
          </div>

          <!-- Skills Filter -->
          <div class="flex flex-wrap justify-center gap-4 mb-12">
            <button class="skill-filter-btn active" data-filter="all">
              Semua
            </button>
            <button class="skill-filter-btn" data-filter="soft-skill">
              Soft Skill
            </button>
            <button class="skill-filter-btn" data-filter="hard-skill">
              Hard Skill
            </button>
          </div>

          <!-- Skills Grid -->
          <div
            id="skills-grid"
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            <!-- Soft Skills -->
            <div class="skill-card card" data-category="soft-skill">
              <div class="text-center space-y-4">
                <div
                  class="w-16 h-16 bg-primary-dark dark:bg-primary-light rounded-full flex items-center justify-center mx-auto"
                >
                  <svg
                    class="w-8 h-8 text-primary-light dark:text-primary-dark"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    ></path>
                  </svg>
                </div>
                <h4
                  class="text-xl font-body font-body-semibold text-primary-dark dark:text-primary-light"
                >
                  Komunikasi
                </h4>
                <p class="text-gray-default font-body-regular">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed
                  do eiusmod tempor incididunt.
                </p>
              </div>
            </div>

            <div class="skill-card card" data-category="soft-skill">
              <div class="text-center space-y-4">
                <div
                  class="w-16 h-16 bg-primary-dark dark:bg-primary-light rounded-full flex items-center justify-center mx-auto"
                >
                  <svg
                    class="w-8 h-8 text-primary-light dark:text-primary-dark"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"
                    ></path>
                  </svg>
                </div>
                <h4
                  class="text-xl font-body font-body-semibold text-primary-dark dark:text-primary-light"
                >
                  Kepemimpinan
                </h4>
                <p class="text-gray-default font-body-regular">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed
                  do eiusmod tempor incididunt.
                </p>
              </div>
            </div>

            <div class="skill-card card" data-category="soft-skill">
              <div class="text-center space-y-4">
                <div
                  class="w-16 h-16 bg-primary-dark dark:bg-primary-light rounded-full flex items-center justify-center mx-auto"
                >
                  <svg
                    class="w-8 h-8 text-primary-light dark:text-primary-dark"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
                    ></path>
                  </svg>
                </div>
                <h4
                  class="text-xl font-body font-body-semibold text-primary-dark dark:text-primary-light"
                >
                  Manajemen Waktu
                </h4>
                <p class="text-gray-default font-body-regular">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed
                  do eiusmod tempor incididunt.
                </p>
              </div>
            </div>

            <!-- Hard Skills -->
            <div class="skill-card card" data-category="hard-skill">
              <div class="text-center space-y-4">
                <div
                  class="w-16 h-16 bg-primary-dark dark:bg-primary-light rounded-full flex items-center justify-center mx-auto"
                >
                  <svg
                    class="w-8 h-8 text-primary-light dark:text-primary-dark"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z"
                    ></path>
                  </svg>
                </div>
                <h4
                  class="text-xl font-body font-body-semibold text-primary-dark dark:text-primary-light"
                >
                  Programming
                </h4>
                <p class="text-gray-default font-body-regular">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed
                  do eiusmod tempor incididunt.
                </p>
              </div>
            </div>

            <div class="skill-card card" data-category="hard-skill">
              <div class="text-center space-y-4">
                <div
                  class="w-16 h-16 bg-primary-dark dark:bg-primary-light rounded-full flex items-center justify-center mx-auto"
                >
                  <svg
                    class="w-8 h-8 text-primary-light dark:text-primary-dark"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
                <h4
                  class="text-xl font-body font-body-semibold text-primary-dark dark:text-primary-light"
                >
                  UI/UX Design
                </h4>
                <p class="text-gray-default font-body-regular">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed
                  do eiusmod tempor incididunt.
                </p>
              </div>
            </div>

            <div class="skill-card card" data-category="hard-skill">
              <div class="text-center space-y-4">
                <div
                  class="w-16 h-16 bg-primary-dark dark:bg-primary-light rounded-full flex items-center justify-center mx-auto"
                >
                  <svg
                    class="w-8 h-8 text-primary-light dark:text-primary-dark"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
                <h4
                  class="text-xl font-body font-body-semibold text-primary-dark dark:text-primary-light"
                >
                  Cyber Security
                </h4>
                <p class="text-gray-default font-body-regular">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed
                  do eiusmod tempor incididunt.
                </p>
              </div>
            </div>
          </div>

          <!-- Load More Button -->
          <div class="text-center mt-12">
            <button id="load-more-skills" class="btn-secondary hidden">
              Lihat selengkapnya
            </button>
          </div>
        </div>
      </section>
    </main>

    <!-- Back to Top Button -->
    <button
      id="back-to-top"
      class="fixed bottom-8 right-8 w-12 h-12 bg-primary-dark dark:bg-primary-light text-primary-light dark:text-primary-dark rounded-full shadow-button hover:shadow-button-hover transition-all duration-300 opacity-0 invisible hover:scale-110 z-navbar"
    >
      <svg
        class="w-6 h-6 mx-auto"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M5 10l7-7m0 0l7 7m-7-7v18"
        ></path>
      </svg>
    </button>

    <!-- Notification Container -->
    <div
      id="notification-container"
      class="fixed top-20 right-4 z-notification space-y-2"
    ></div>

    <!-- JavaScript -->
    <script src="./dist/js/script.js"></script>
  </body>
</html>
