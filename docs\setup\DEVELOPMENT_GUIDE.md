# Development Guide - Personal Portfolio Muhammad S<PERSON><PERSON>

## 🚀 Quick Start

### Prerequisites
- Node.js >= 16.0.0
- npm >= 8.0.0

### Installation
```bash
# Clone repository
git clone https://github.com/muhammad<PERSON>aami<PERSON>uzhaffar/personal-portfolio.git
cd personal-portfolio

# Install dependencies
npm install
```

### Development
```bash
# Start development server
npm run dev

# Build CSS only
npm run build:css

# Watch CSS changes
npm run build:css:watch

# Format code
npm run format
```

### Production Build
```bash
# Build for production
npm run build

# Serve production build locally
npm run serve
```

## 📁 Project Structure

```
personal-portfolio/
├── assets/                     # Static assets
│   ├── icons/                  # Icons and SVG files
│   ├── images/                 # Images and photos
│   ├── fonts/                  # Custom fonts
│   └── videos/                 # Video files
├── config/                     # Configuration files
│   ├── environments/           # Environment configs
│   └── tailwind.config.js      # TailwindCSS configuration
├── src/                        # Source code
│   ├── css/                    # CSS source files
│   ├── js/                     # JavaScript modules
│   └── scss/                   # SCSS structure (future)
├── dist/                       # Build output
│   ├── css/                    # Compiled CSS
│   └── js/                     # Compiled JavaScript
├── tools/                      # Build tools
│   ├── build.js               # Production build script
│   └── dev.js                 # Development server
└── docs/                       # Documentation
```

## 🎨 Design System

### Colors
- **Primary Dark**: #181818
- **Primary Light**: #FFFFFF
- **Gray Default**: #6B7280

### Typography
- **Headings**: Merriweather (Bold)
- **Body**: Open Sans (Regular)

### Breakpoints
- **Mobile**: 320px - 767px
- **Tablet**: 768px - 1023px
- **Desktop**: 1024px+

## 🔧 Development Features

### Hot Reload
Development server includes:
- CSS hot reload
- File watching
- Auto browser refresh

### Security Features
- Right-click protection
- Developer tools blocking
- Copy-paste prevention
- Text selection blocking

### Performance
- Optimized CSS compilation
- Image optimization ready
- Lazy loading support
- Smooth scrolling

## 📝 Content Management

### Adding Content
1. **Profile Image**: Add `muhammadsyaamilmuzhaffar.png` to `assets/images/profile/`
2. **Social Media Icons**: Add SVG icons to `assets/icons/social-media/`
3. **Tech Stack Icons**: Add SVG icons to `assets/images/tech-stacks/`

### Updating Social Links
Edit the `SOCIAL_LINKS` object in `src/js/utils/constants.js`:

```javascript
export const SOCIAL_LINKS = {
  CREDLY: 'https://credly.com/users/your-profile',
  GITHUB: 'https://github.com/your-username',
  // ... other links
};
```

### Customizing Content
- **About Section**: Edit lorem ipsum text in `index.html`
- **Skills**: Add/modify skill cards in the skills section
- **Projects**: Will be added in future sections
- **Certificates**: Will be added in future sections

## 🛠️ Build Process

### Development Build
```bash
npm run dev
```
- Starts development server on http://localhost:3000
- Watches for file changes
- Hot reloads CSS
- Enables debug mode

### Production Build
```bash
npm run build
```
- Minifies CSS and JavaScript
- Optimizes images
- Generates build report
- Creates production-ready files

## 🔒 Security Implementation

### Features Implemented
- **Anti Right-Click**: Prevents context menu
- **Developer Tools Protection**: Blocks F12, Ctrl+Shift+I
- **Copy Protection**: Prevents Ctrl+C, Ctrl+V, Ctrl+A
- **Text Selection**: Disabled with user-select: none
- **Console Warnings**: Security messages in console

### Customization
Security features can be configured in `config/environments/production.js`:

```javascript
security: {
  disableRightClick: true,
  disableInspect: true,
  disableCopy: true,
  enableContentProtection: true
}
```

## 📱 Responsive Design

### Mobile-First Approach
- Base styles for mobile (320px+)
- Progressive enhancement for larger screens
- Touch-friendly interface elements

### Breakpoint Strategy
- **xs**: 320px (extra small phones)
- **sm**: 640px (small phones)
- **md**: 768px (tablets)
- **lg**: 1024px (laptops)
- **xl**: 1280px (desktops)
- **2xl**: 1536px (large desktops)

## 🎭 Theme System

### Light/Dark Mode
- Automatic theme detection
- Manual toggle (desktop: sun/moon icon, mobile: toggle switch)
- Persistent theme storage in localStorage
- Smooth transitions between themes

### Customization
Theme colors can be modified in `config/tailwind.config.js`:

```javascript
colors: {
  primary: {
    dark: '#181818',
    light: '#FFFFFF'
  }
}
```

## 🚀 Deployment

### GitHub Pages
1. Build the project: `npm run build`
2. Copy `dist/` contents to deployment folder
3. Push to GitHub Pages branch

### Custom Hosting
1. Run production build: `npm run build`
2. Upload `dist/` folder contents to web server
3. Configure server for SPA routing (if needed)

## 🐛 Troubleshooting

### Common Issues

**CSS not updating:**
```bash
npm run build:css
```

**JavaScript errors:**
- Check browser console for errors
- Ensure all DOM elements exist before accessing

**Build failures:**
- Check Node.js version (>= 16.0.0)
- Clear node_modules and reinstall: `rm -rf node_modules && npm install`

### Debug Mode
Enable debug mode in development:
```javascript
// In config/environments/development.js
debug: {
  enabled: true,
  verbose: true,
  showPerformance: true
}
```

## 📞 Support

For issues or questions:
1. Check this documentation
2. Review console errors
3. Check GitHub issues
4. Contact developer: <EMAIL>
