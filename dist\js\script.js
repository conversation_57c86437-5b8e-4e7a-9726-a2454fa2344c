/**
 * Main JavaScript File
 * Personal Portfolio Muhammad S<PERSON>
 *
 * Fitur utama:
 * - Navigation dan smooth scrolling
 * - Dark/Light mode toggle
 * - Mobile menu functionality
 * - Security features (anti right-click, inspect, copy-paste)
 * - Scroll animations
 * - Form handling
 * - Notification system
 */

// Import modules (akan diimplementasi dengan ES6 modules)
// import { ThemeManager } from '../src/js/modules/theme.js';
// import { SecurityManager } from '../src/js/modules/security.js';
// import { NavigationManager } from '../src/js/modules/navigation.js';

// Global variables
let isLoading = true;
let currentTheme = "light";
let mobileMenuOpen = false;
let lastScrollY = 0;

// DOM Elements
const elements = {
  loadingScreen: null,
  navbar: null,
  mobileMenuToggle: null,
  mobileMenu: null,
  hamburgerIcon: null,
  closeIcon: null,
  themeToggleDesktop: null,
  themeToggleMobile: null,
  sunIcon: null,
  moonIcon: null,
  backToTopBtn: null,
  brandLogo: null,
  navLinks: null,
  mobileNavLinks: null,
  skillFilterBtns: null,
  skillCards: null,
  notificationContainer: null,
};

/**
 * Initialize application
 */
function init() {
  console.log("🚀 Initializing Personal Portfolio...");

  // Cache DOM elements
  cacheDOMElements();

  // Initialize theme
  initializeTheme();

  // Initialize security features
  initializeSecurity();

  // Initialize navigation
  initializeNavigation();

  // Initialize scroll effects
  initializeScrollEffects();

  // Initialize skills filter
  initializeSkillsFilter();

  // Initialize loading screen
  initializeLoadingScreen();

  // Initialize event listeners
  initializeEventListeners();

  console.log("✅ Portfolio initialized successfully!");
}

/**
 * Cache DOM elements untuk performance
 */
function cacheDOMElements() {
  elements.loadingScreen = document.getElementById("loading-screen");
  elements.navbar = document.getElementById("navbar");
  elements.mobileMenuToggle = document.getElementById("mobile-menu-toggle");
  elements.mobileMenu = document.getElementById("mobile-menu");
  elements.hamburgerIcon = document.getElementById("hamburger-icon");
  elements.closeIcon = document.getElementById("close-icon");
  elements.themeToggleDesktop = document.getElementById("theme-toggle-desktop");
  elements.themeToggleMobile = document.getElementById("theme-toggle-mobile");
  elements.sunIcon = document.getElementById("sun-icon");
  elements.moonIcon = document.getElementById("moon-icon");
  elements.backToTopBtn = document.getElementById("back-to-top");
  elements.brandLogo = document.getElementById("brand-logo");
  elements.navLinks = document.querySelectorAll(".nav-link");
  elements.mobileNavLinks = document.querySelectorAll(".nav-link-mobile");
  elements.skillFilterBtns = document.querySelectorAll(".skill-filter-btn");
  elements.skillCards = document.querySelectorAll(".skill-card");
  elements.notificationContainer = document.getElementById(
    "notification-container"
  );
}

/**
 * Initialize theme system
 */
function initializeTheme() {
  // Get saved theme from localStorage
  const savedTheme = localStorage.getItem("theme") || "light";
  currentTheme = savedTheme;

  // Apply theme
  applyTheme(currentTheme);

  // Set mobile toggle state
  if (elements.themeToggleMobile) {
    elements.themeToggleMobile.checked = currentTheme === "dark";
  }
}

/**
 * Apply theme to document
 */
function applyTheme(theme) {
  document.documentElement.setAttribute("data-theme", theme);
  localStorage.setItem("theme", theme);
  currentTheme = theme;

  // Update icon visibility
  if (elements.sunIcon && elements.moonIcon) {
    if (theme === "dark") {
      elements.sunIcon.classList.remove("hidden");
      elements.sunIcon.classList.add("block");
      elements.moonIcon.classList.remove("block");
      elements.moonIcon.classList.add("hidden");
    } else {
      elements.sunIcon.classList.remove("block");
      elements.sunIcon.classList.add("hidden");
      elements.moonIcon.classList.remove("hidden");
      elements.moonIcon.classList.add("block");
    }
  }
}

/**
 * Toggle theme
 */
function toggleTheme() {
  const newTheme = currentTheme === "light" ? "dark" : "light";
  applyTheme(newTheme);

  // Update mobile toggle
  if (elements.themeToggleMobile) {
    elements.themeToggleMobile.checked = newTheme === "dark";
  }

  // Add animation effect
  document.body.style.transition =
    "background-color 0.3s ease, color 0.3s ease";
  setTimeout(() => {
    document.body.style.transition = "";
  }, 300);
}

/**
 * Initialize security features
 */
function initializeSecurity() {
  // Disable right-click
  document.addEventListener("contextmenu", function (e) {
    e.preventDefault();
    showNotification("Klik kanan tidak diizinkan pada situs ini.", "warning");
    return false;
  });

  // Disable F12, Ctrl+Shift+I, Ctrl+U
  document.addEventListener("keydown", function (e) {
    // F12
    if (e.key === "F12") {
      e.preventDefault();
      showNotification(
        "Developer tools tidak diizinkan pada situs ini.",
        "warning"
      );
      return false;
    }

    // Ctrl+Shift+I (Developer Tools)
    if (e.ctrlKey && e.shiftKey && e.key === "I") {
      e.preventDefault();
      showNotification(
        "Developer tools tidak diizinkan pada situs ini.",
        "warning"
      );
      return false;
    }

    // Ctrl+U (View Source)
    if (e.ctrlKey && e.key === "u") {
      e.preventDefault();
      showNotification(
        "Melihat source code tidak diizinkan pada situs ini.",
        "warning"
      );
      return false;
    }

    // Ctrl+A (Select All)
    if (e.ctrlKey && e.key === "a") {
      e.preventDefault();
      showNotification("Select all tidak diizinkan pada situs ini.", "warning");
      return false;
    }

    // Ctrl+C (Copy)
    if (e.ctrlKey && e.key === "c") {
      e.preventDefault();
      showNotification("Copy tidak diizinkan pada situs ini.", "warning");
      return false;
    }

    // Ctrl+V (Paste)
    if (e.ctrlKey && e.key === "v") {
      e.preventDefault();
      showNotification("Paste tidak diizinkan pada situs ini.", "warning");
      return false;
    }
  });

  // Disable text selection
  document.onselectstart = function () {
    showNotification(
      "Text selection tidak diizinkan pada situs ini.",
      "warning"
    );
    return false;
  };

  // Disable drag
  document.ondragstart = function () {
    return false;
  };

  // Console warning
  console.log(
    "%c⚠️ PERINGATAN KEAMANAN!",
    "color: red; font-size: 20px; font-weight: bold;"
  );
  console.log(
    "%cSitus ini dilindungi oleh sistem keamanan. Segala bentuk upaya untuk mengakses atau memodifikasi kode akan dipantau.",
    "color: red; font-size: 14px;"
  );
}

/**
 * Initialize navigation functionality
 */
function initializeNavigation() {
  // Mobile menu toggle
  if (elements.mobileMenuToggle) {
    elements.mobileMenuToggle.addEventListener("click", toggleMobileMenu);
  }

  // Brand logo click - scroll to top
  if (elements.brandLogo) {
    elements.brandLogo.addEventListener("click", function (e) {
      e.preventDefault();
      smoothScrollToTop();
    });
  }

  // Navigation links smooth scroll
  elements.navLinks.forEach((link) => {
    link.addEventListener("click", function (e) {
      e.preventDefault();
      const targetId = this.getAttribute("href").substring(1);
      smoothScrollToSection(targetId);
    });
  });

  // Mobile navigation links
  elements.mobileNavLinks.forEach((link) => {
    link.addEventListener("click", function (e) {
      e.preventDefault();
      const targetId = this.getAttribute("href").substring(1);
      smoothScrollToSection(targetId);
      closeMobileMenu();
    });
  });
}

/**
 * Toggle mobile menu
 */
function toggleMobileMenu() {
  mobileMenuOpen = !mobileMenuOpen;

  if (mobileMenuOpen) {
    openMobileMenu();
  } else {
    closeMobileMenu();
  }
}

/**
 * Open mobile menu
 */
function openMobileMenu() {
  if (elements.mobileMenu) {
    elements.mobileMenu.classList.remove("hidden");
    elements.mobileMenu.classList.add("animate-slide-down");
  }

  // Toggle icons
  if (elements.hamburgerIcon && elements.closeIcon) {
    elements.hamburgerIcon.classList.add("hidden");
    elements.closeIcon.classList.remove("hidden");
  }

  mobileMenuOpen = true;
}

/**
 * Close mobile menu
 */
function closeMobileMenu() {
  if (elements.mobileMenu) {
    elements.mobileMenu.classList.add("hidden");
    elements.mobileMenu.classList.remove("animate-slide-down");
  }

  // Toggle icons
  if (elements.hamburgerIcon && elements.closeIcon) {
    elements.hamburgerIcon.classList.remove("hidden");
    elements.closeIcon.classList.add("hidden");
  }

  mobileMenuOpen = false;
}

/**
 * Smooth scroll to section
 */
function smoothScrollToSection(targetId) {
  const target = document.getElementById(targetId);
  if (target) {
    const navbarHeight = elements.navbar ? elements.navbar.offsetHeight : 80;
    const targetPosition = target.offsetTop - navbarHeight;

    window.scrollTo({
      top: targetPosition,
      behavior: "smooth",
    });
  }
}

/**
 * Smooth scroll to top
 */
function smoothScrollToTop() {
  window.scrollTo({
    top: 0,
    behavior: "smooth",
  });
}

/**
 * Initialize scroll effects
 */
function initializeScrollEffects() {
  window.addEventListener("scroll", throttle(handleScroll, 16)); // 60fps
}

/**
 * Handle scroll events
 */
function handleScroll() {
  const currentScrollY = window.scrollY;

  // Navbar transparency effect
  handleNavbarScroll(currentScrollY);

  // Back to top button visibility
  handleBackToTopButton(currentScrollY);

  // Update active navigation link
  updateActiveNavLink();

  lastScrollY = currentScrollY;
}

/**
 * Handle navbar scroll effects
 */
function handleNavbarScroll(scrollY) {
  if (!elements.navbar) return;

  if (scrollY > 100) {
    // Add glass effect when scrolled
    elements.navbar.classList.add("glass");
    elements.navbar.style.backgroundColor =
      currentTheme === "dark"
        ? "rgba(24, 24, 24, 0.9)"
        : "rgba(255, 255, 255, 0.9)";
  } else {
    // Remove glass effect at top
    elements.navbar.classList.remove("glass");
    elements.navbar.style.backgroundColor =
      currentTheme === "dark"
        ? "rgba(24, 24, 24, 0.9)"
        : "rgba(255, 255, 255, 0.9)";
  }
}

/**
 * Handle back to top button visibility
 */
function handleBackToTopButton(scrollY) {
  if (!elements.backToTopBtn) return;

  if (scrollY > 300) {
    elements.backToTopBtn.classList.remove("opacity-0", "invisible");
    elements.backToTopBtn.classList.add("opacity-100", "visible");
  } else {
    elements.backToTopBtn.classList.add("opacity-0", "invisible");
    elements.backToTopBtn.classList.remove("opacity-100", "visible");
  }
}

/**
 * Update active navigation link based on scroll position
 */
function updateActiveNavLink() {
  const sections = [
    "hero",
    "about",
    "skills",
    "projects",
    "certificates",
    "tech-stack",
    "contact",
  ];
  const navbarHeight = elements.navbar ? elements.navbar.offsetHeight : 80;

  let currentSection = "";

  sections.forEach((sectionId) => {
    const section = document.getElementById(sectionId);
    if (section) {
      const sectionTop = section.offsetTop - navbarHeight - 100;
      const sectionBottom = sectionTop + section.offsetHeight;

      if (window.scrollY >= sectionTop && window.scrollY < sectionBottom) {
        currentSection = sectionId;
      }
    }
  });

  // Update active states
  elements.navLinks.forEach((link) => {
    const href = link.getAttribute("href");
    if (href === `#${currentSection}`) {
      link.classList.add("active");
    } else {
      link.classList.remove("active");
    }
  });
}

/**
 * Initialize skills filter functionality
 */
function initializeSkillsFilter() {
  elements.skillFilterBtns.forEach((btn) => {
    btn.addEventListener("click", function () {
      const filter = this.getAttribute("data-filter");
      filterSkills(filter);
      updateActiveFilterBtn(this);
    });
  });
}

/**
 * Filter skills by category
 */
function filterSkills(filter) {
  elements.skillCards.forEach((card) => {
    const category = card.getAttribute("data-category");

    if (filter === "all" || category === filter) {
      card.style.display = "block";
      card.classList.add("animate-fade-in");
    } else {
      card.style.display = "none";
      card.classList.remove("animate-fade-in");
    }
  });
}

/**
 * Update active filter button
 */
function updateActiveFilterBtn(activeBtn) {
  elements.skillFilterBtns.forEach((btn) => {
    btn.classList.remove("active");
  });
  activeBtn.classList.add("active");
}

/**
 * Initialize loading screen
 */
function initializeLoadingScreen() {
  // Simulate loading time
  setTimeout(() => {
    hideLoadingScreen();
  }, 1500);
}

/**
 * Hide loading screen
 */
function hideLoadingScreen() {
  if (elements.loadingScreen) {
    elements.loadingScreen.classList.add("animate-fade-out");
    setTimeout(() => {
      elements.loadingScreen.style.display = "none";
      isLoading = false;
    }, 300);
  }
}

/**
 * Initialize event listeners
 */
function initializeEventListeners() {
  // Theme toggle events
  if (elements.themeToggleDesktop) {
    elements.themeToggleDesktop.addEventListener("click", toggleTheme);
  }

  if (elements.themeToggleMobile) {
    elements.themeToggleMobile.addEventListener("change", toggleTheme);
  }

  // Back to top button
  if (elements.backToTopBtn) {
    elements.backToTopBtn.addEventListener("click", smoothScrollToTop);
  }

  // Close mobile menu when clicking outside
  document.addEventListener("click", function (e) {
    if (
      mobileMenuOpen &&
      !elements.mobileMenu.contains(e.target) &&
      !elements.mobileMenuToggle.contains(e.target)
    ) {
      closeMobileMenu();
    }
  });

  // Handle window resize
  window.addEventListener("resize", debounce(handleResize, 250));
}

/**
 * Handle window resize
 */
function handleResize() {
  // Close mobile menu on desktop
  if (window.innerWidth >= 1024 && mobileMenuOpen) {
    closeMobileMenu();
  }
}

/**
 * Show notification
 */
function showNotification(message, type = "info", duration = 3000) {
  if (!elements.notificationContainer) return;

  const notification = document.createElement("div");
  notification.className = `notification notification-${type} bg-primary-light dark:bg-primary-dark border border-gray-default/20 rounded-lg p-4 shadow-card animate-slide-left`;

  const icon = getNotificationIcon(type);

  notification.innerHTML = `
    <div class="flex items-center space-x-3">
      <div class="flex-shrink-0">
        ${icon}
      </div>
      <div class="flex-1">
        <p class="text-sm font-medium text-primary-dark dark:text-primary-light">${message}</p>
      </div>
      <button class="notification-close flex-shrink-0 text-gray-default hover:text-primary-dark dark:hover:text-primary-light transition-colors duration-200">
        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
        </svg>
      </button>
    </div>
  `;

  // Add close functionality
  const closeBtn = notification.querySelector(".notification-close");
  closeBtn.addEventListener("click", () => {
    removeNotification(notification);
  });

  // Add to container
  elements.notificationContainer.appendChild(notification);

  // Auto remove after duration
  setTimeout(() => {
    removeNotification(notification);
  }, duration);
}

/**
 * Get notification icon based on type
 */
function getNotificationIcon(type) {
  const icons = {
    success: `<svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
    </svg>`,
    warning: `<svg class="w-5 h-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
      <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
    </svg>`,
    error: `<svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
    </svg>`,
    info: `<svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
      <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
    </svg>`,
  };

  return icons[type] || icons.info;
}

/**
 * Remove notification
 */
function removeNotification(notification) {
  if (notification && notification.parentNode) {
    notification.classList.add("animate-slide-right");
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }
}

/**
 * Throttle function for performance optimization
 */
function throttle(func, limit) {
  let inThrottle;
  return function () {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

/**
 * Debounce function for performance optimization
 */
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Initialize when DOM is loaded
document.addEventListener("DOMContentLoaded", init);

// Initialize when page is fully loaded
window.addEventListener("load", function () {
  console.log("🎉 Portfolio fully loaded!");
});
