/**
 * Development Environment Configuration
 * Konfigurasi untuk environment development
 */

const developmentConfig = {
  // Environment settings
  NODE_ENV: 'development',
  
  // Development server settings
  server: {
    port: 3000,
    host: 'localhost',
    hot: true,
    open: true
  },
  
  // Build settings
  build: {
    sourceMaps: true,
    minify: false,
    watch: true
  },
  
  // Security settings (relaxed for development)
  security: {
    disableRightClick: false,
    disableInspect: false,
    disableCopy: false
  },
  
  // Debug settings
  debug: {
    enabled: true,
    verbose: true,
    showPerformance: true
  }
};

module.exports = developmentConfig;
