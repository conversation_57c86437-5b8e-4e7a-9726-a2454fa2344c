/**
 * Production Environment Configuration
 * Konfigurasi untuk environment production
 */

const productionConfig = {
  // Environment settings
  NODE_ENV: 'production',
  
  // Build settings
  build: {
    sourceMaps: false,
    minify: true,
    watch: false,
    optimize: true
  },
  
  // Security settings (strict for production)
  security: {
    disableRightClick: true,
    disableInspect: true,
    disableCopy: true,
    enableContentProtection: true
  },
  
  // Performance settings
  performance: {
    enableCompression: true,
    enableCaching: true,
    lazyLoading: true
  },
  
  // Debug settings
  debug: {
    enabled: false,
    verbose: false,
    showPerformance: false
  }
};

module.exports = productionConfig;
