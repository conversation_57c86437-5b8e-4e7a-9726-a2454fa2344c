/**
 * TailwindCSS Configuration
 * Konfigurasi TailwindCSS untuk Personal Portfolio Muhammad <PERSON>
 */

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{html,js,ts,jsx,tsx}",
    "./dist/**/*.{html,js}",
  ],

  // Dark mode configuration
  darkMode: ["class", '[data-theme="dark"]'],

  theme: {
    extend: {
      // Custom colors sesuai spesifikasi
      colors: {
        primary: {
          dark: "#181818",
          light: "#FFFFFF",
        },
        gray: {
          default: "#6B7280",
          light: "#F3F4F6",
          dark: "#374151",
        },
        shadow: {
          light: "rgba(24, 24, 24, 0.1)",
          dark: "rgba(255, 255, 255, 0.1)",
        },
      },

      // Custom fonts sesuai spesifikasi
      fontFamily: {
        heading: ["Merriweather", "serif"],
        body: ["Open Sans", "sans-serif"],
        sans: ["Open Sans", "ui-sans-serif", "system-ui"],
        serif: ["Merriweather", "ui-serif", "Georgia"],
      },

      // Custom font weights
      fontWeight: {
        "heading-bold": "700",
        "body-regular": "400",
        "body-medium": "500",
        "body-semibold": "600",
      },

      // Custom spacing untuk presisi layout
      spacing: {
        18: "4.5rem",
        22: "5.5rem",
        26: "6.5rem",
        30: "7.5rem",
        34: "8.5rem",
        38: "9.5rem",
        42: "10.5rem",
        46: "11.5rem",
        50: "12.5rem",
        54: "13.5rem",
        58: "14.5rem",
        62: "15.5rem",
        66: "16.5rem",
        70: "17.5rem",
        74: "18.5rem",
        78: "19.5rem",
        82: "20.5rem",
        86: "21.5rem",
        90: "22.5rem",
        94: "23.5rem",
        98: "24.5rem",
      },

      // Custom breakpoints untuk responsive design
      screens: {
        xs: "320px",
        sm: "640px",
        md: "768px",
        lg: "1024px",
        xl: "1280px",
        "2xl": "1536px",
        mobile: { max: "767px" },
        tablet: { min: "768px", max: "1023px" },
        desktop: { min: "1024px" },
      },

      // Custom animations
      animation: {
        "fade-in": "fadeIn 0.3s ease-in-out",
        "fade-out": "fadeOut 0.3s ease-in-out",
        "slide-up": "slideUp 0.3s ease-out",
        "slide-down": "slideDown 0.3s ease-out",
        "slide-left": "slideLeft 0.3s ease-out",
        "slide-right": "slideRight 0.3s ease-out",
        "scale-up": "scaleUp 0.2s ease-out",
        "scale-down": "scaleDown 0.2s ease-out",
        "bounce-gentle": "bounceGentle 0.6s ease-in-out",
        "pulse-gentle": "pulseGentle 2s ease-in-out infinite",
        float: "float 3s ease-in-out infinite",
        glow: "glow 2s ease-in-out infinite alternate",
        "hamburger-to-x": "hamburgerToX 0.3s ease-in-out",
        "x-to-hamburger": "xToHamburger 0.3s ease-in-out",
      },

      // Custom keyframes untuk animations
      keyframes: {
        fadeIn: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        fadeOut: {
          "0%": { opacity: "1" },
          "100%": { opacity: "0" },
        },
        slideUp: {
          "0%": { transform: "translateY(20px)", opacity: "0" },
          "100%": { transform: "translateY(0)", opacity: "1" },
        },
        slideDown: {
          "0%": { transform: "translateY(-20px)", opacity: "0" },
          "100%": { transform: "translateY(0)", opacity: "1" },
        },
        slideLeft: {
          "0%": { transform: "translateX(20px)", opacity: "0" },
          "100%": { transform: "translateX(0)", opacity: "1" },
        },
        slideRight: {
          "0%": { transform: "translateX(-20px)", opacity: "0" },
          "100%": { transform: "translateX(0)", opacity: "1" },
        },
        scaleUp: {
          "0%": { transform: "scale(0.95)", opacity: "0" },
          "100%": { transform: "scale(1)", opacity: "1" },
        },
        scaleDown: {
          "0%": { transform: "scale(1.05)", opacity: "0" },
          "100%": { transform: "scale(1)", opacity: "1" },
        },
        bounceGentle: {
          "0%, 100%": { transform: "translateY(0)" },
          "50%": { transform: "translateY(-10px)" },
        },
        pulseGentle: {
          "0%, 100%": { opacity: "1" },
          "50%": { opacity: "0.7" },
        },
        float: {
          "0%, 100%": { transform: "translateY(0px)" },
          "50%": { transform: "translateY(-10px)" },
        },
        glow: {
          "0%": { boxShadow: "0 0 5px rgba(24, 24, 24, 0.2)" },
          "100%": { boxShadow: "0 0 20px rgba(24, 24, 24, 0.4)" },
        },
        hamburgerToX: {
          "0%": { transform: "rotate(0deg)" },
          "100%": { transform: "rotate(45deg)" },
        },
        xToHamburger: {
          "0%": { transform: "rotate(45deg)" },
          "100%": { transform: "rotate(0deg)" },
        },
      },

      // Custom transitions
      transitionDuration: {
        250: "250ms",
        350: "350ms",
        400: "400ms",
        450: "450ms",
        600: "600ms",
        800: "800ms",
        900: "900ms",
        1200: "1200ms",
      },

      // Custom transition timing functions
      transitionTimingFunction: {
        "bounce-in": "cubic-bezier(0.68, -0.55, 0.265, 1.55)",
        smooth: "cubic-bezier(0.4, 0, 0.2, 1)",
        gentle: "cubic-bezier(0.25, 0.46, 0.45, 0.94)",
      },

      // Custom box shadows untuk depth dan elevation
      boxShadow: {
        card: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
        "card-hover":
          "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
        "card-dark":
          "0 4px 6px -1px rgba(255, 255, 255, 0.1), 0 2px 4px -1px rgba(255, 255, 255, 0.06)",
        "card-hover-dark":
          "0 10px 15px -3px rgba(255, 255, 255, 0.1), 0 4px 6px -2px rgba(255, 255, 255, 0.05)",
        navbar:
          "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
        "navbar-dark":
          "0 1px 3px 0 rgba(255, 255, 255, 0.1), 0 1px 2px 0 rgba(255, 255, 255, 0.06)",
        button: "0 2px 4px 0 rgba(0, 0, 0, 0.1)",
        "button-hover": "0 4px 8px 0 rgba(0, 0, 0, 0.15)",
        "glow-light": "0 0 20px rgba(24, 24, 24, 0.3)",
        "glow-dark": "0 0 20px rgba(255, 255, 255, 0.3)",
      },

      // Custom border radius
      borderRadius: {
        card: "12px",
        button: "8px",
        input: "6px",
        badge: "20px",
      },

      // Custom backdrop blur
      backdropBlur: {
        navbar: "10px",
        modal: "20px",
      },

      // Custom z-index values
      zIndex: {
        navbar: "50",
        dropdown: "60",
        modal: "70",
        notification: "80",
        tooltip: "90",
        overlay: "100",
      },

      // Custom line heights
      lineHeight: {
        heading: "1.2",
        body: "1.6",
        relaxed: "1.8",
      },

      // Custom letter spacing
      letterSpacing: {
        heading: "-0.025em",
        body: "0em",
        wide: "0.05em",
      },
    },
  },

  // Custom plugins dan utilities
  plugins: [
    // Plugin untuk custom utilities
    function ({ addUtilities, addComponents, theme }) {
      // Custom utilities untuk glassmorphism effect
      addUtilities({
        ".glass": {
          background: "rgba(255, 255, 255, 0.1)",
          backdropFilter: "blur(10px)",
          border: "1px solid rgba(255, 255, 255, 0.2)",
        },
        ".glass-dark": {
          background: "rgba(24, 24, 24, 0.1)",
          backdropFilter: "blur(10px)",
          border: "1px solid rgba(24, 24, 24, 0.2)",
        },

        // Custom utilities untuk text selection
        ".no-select": {
          "-webkit-user-select": "none",
          "-moz-user-select": "none",
          "-ms-user-select": "none",
          "user-select": "none",
        },

        // Custom utilities untuk scrollbar
        ".scrollbar-hide": {
          "-ms-overflow-style": "none",
          "scrollbar-width": "none",
          "&::-webkit-scrollbar": {
            display: "none",
          },
        },

        // Custom utilities untuk smooth scrolling
        ".scroll-smooth": {
          "scroll-behavior": "smooth",
        },
      });

      // Custom components
      addComponents({
        // Button components
        ".btn-primary": {
          padding: theme("spacing.3") + " " + theme("spacing.6"),
          backgroundColor: theme("colors.primary.dark"),
          color: theme("colors.primary.light"),
          borderRadius: theme("borderRadius.button"),
          fontWeight: theme("fontWeight.body-medium"),
          transition: "all 0.3s ease",
          "&:hover": {
            backgroundColor: theme("colors.gray.dark"),
            boxShadow: theme("boxShadow.button-hover"),
          },
        },

        ".btn-secondary": {
          padding: theme("spacing.3") + " " + theme("spacing.6"),
          backgroundColor: "transparent",
          color: theme("colors.primary.dark"),
          border: "2px solid " + theme("colors.primary.dark"),
          borderRadius: theme("borderRadius.button"),
          fontWeight: theme("fontWeight.body-medium"),
          transition: "all 0.3s ease",
          "&:hover": {
            backgroundColor: theme("colors.primary.dark"),
            color: theme("colors.primary.light"),
          },
        },

        // Card components
        ".card": {
          backgroundColor: theme("colors.primary.light"),
          borderRadius: theme("borderRadius.card"),
          boxShadow: theme("boxShadow.card"),
          padding: theme("spacing.6"),
          transition: "all 0.3s ease",
          "&:hover": {
            boxShadow: theme("boxShadow.card-hover"),
            transform: "translateY(-4px)",
          },
        },

        ".card-dark": {
          backgroundColor: theme("colors.primary.dark"),
          color: theme("colors.primary.light"),
          borderRadius: theme("borderRadius.card"),
          boxShadow: theme("boxShadow.card-dark"),
          padding: theme("spacing.6"),
          transition: "all 0.3s ease",
          "&:hover": {
            boxShadow: theme("boxShadow.card-hover-dark"),
            transform: "translateY(-4px)",
          },
        },
      });
    },
  ],
};
