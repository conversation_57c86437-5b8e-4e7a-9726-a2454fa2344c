/**
 * Helper Functions
 * Fungsi-fungsi pembantu yang dapat digunakan di seluruh aplikasi
 */

import { ANIMATIONS, BREAKPOINTS } from './constants.js';

/**
 * Smooth scroll ke element tertentu
 * @param {string} targetId - ID element target
 * @param {number} offset - Offset dari top (default: 0)
 */
export function smoothScrollTo(targetId, offset = 0) {
  const target = document.getElementById(targetId);
  if (target) {
    const targetPosition = target.offsetTop - offset;
    window.scrollTo({
      top: targetPosition,
      behavior: 'smooth'
    });
  }
}

/**
 * Debounce function untuk optimasi performance
 * @param {Function} func - Function yang akan di-debounce
 * @param {number} wait - Waktu tunggu dalam ms
 * @returns {Function} - Debounced function
 */
export function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * Throttle function untuk optimasi performance
 * @param {Function} func - Function yang akan di-throttle
 * @param {number} limit - Limit waktu dalam ms
 * @returns {Function} - Throttled function
 */
export function throttle(func, limit) {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * Cek apakah device adalah mobile
 * @returns {boolean} - True jika mobile
 */
export function isMobile() {
  return window.innerWidth <= BREAKPOINTS.TABLET;
}

/**
 * Cek apakah device adalah tablet
 * @returns {boolean} - True jika tablet
 */
export function isTablet() {
  return window.innerWidth > BREAKPOINTS.MOBILE && window.innerWidth <= BREAKPOINTS.DESKTOP;
}

/**
 * Cek apakah device adalah desktop
 * @returns {boolean} - True jika desktop
 */
export function isDesktop() {
  return window.innerWidth > BREAKPOINTS.DESKTOP;
}

/**
 * Format text untuk mencegah XSS
 * @param {string} text - Text yang akan diformat
 * @returns {string} - Text yang sudah aman
 */
export function sanitizeText(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

/**
 * Validate email format
 * @param {string} email - Email yang akan divalidasi
 * @returns {boolean} - True jika email valid
 */
export function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Show notification
 * @param {string} message - Pesan notifikasi
 * @param {string} type - Tipe notifikasi (success, error, warning)
 * @param {number} duration - Durasi tampil dalam ms
 */
export function showNotification(message, type = 'success', duration = 3000) {
  // Implementation akan ditambahkan di notification module
  console.log(`${type.toUpperCase()}: ${message}`);
}

/**
 * Get current theme
 * @returns {string} - 'light' atau 'dark'
 */
export function getCurrentTheme() {
  return localStorage.getItem('theme') || 'light';
}

/**
 * Set theme
 * @param {string} theme - 'light' atau 'dark'
 */
export function setTheme(theme) {
  localStorage.setItem('theme', theme);
  document.documentElement.setAttribute('data-theme', theme);
}
