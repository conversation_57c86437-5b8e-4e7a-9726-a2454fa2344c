/**
 * Production Build Script
 * Script untuk build production dengan optimasi dan minifikasi
 */

const fs = require("fs");
const path = require("path");
const { spawn } = require("child_process");

// Import environment configuration
const prodConfig = require("../config/environments/production.js");

class ProductionBuilder {
  constructor() {
    this.buildStartTime = Date.now();
    this.buildStats = {
      cssSize: 0,
      jsSize: 0,
      htmlSize: 0,
      totalAssets: 0,
    };
  }

  /**
   * Start production build
   */
  async build() {
    console.log("🏗️  Starting production build...");
    console.log(`📍 Environment: ${prodConfig.NODE_ENV}`);

    try {
      // Clean dist directory
      await this.cleanDist();

      // Build CSS
      await this.buildCSS();

      // Build JavaScript
      await this.buildJS();

      // Optimize HTML
      await this.optimizeHTML();

      // Copy assets
      await this.copyAssets();

      // Generate build report
      this.generateBuildReport();

      console.log("✅ Production build completed successfully!");
    } catch (error) {
      console.error("❌ Production build failed:", error);
      process.exit(1);
    }
  }

  /**
   * Clean dist directory
   */
  async cleanDist() {
    console.log("🧹 Cleaning dist directory...");

    const distPath = "./dist";
    if (fs.existsSync(distPath)) {
      fs.rmSync(distPath, { recursive: true, force: true });
    }

    // Recreate dist structure
    fs.mkdirSync("./dist", { recursive: true });
    fs.mkdirSync("./dist/css", { recursive: true });
    fs.mkdirSync("./dist/js", { recursive: true });
    fs.mkdirSync("./dist/assets", { recursive: true });

    console.log("✅ Dist directory cleaned");
  }

  /**
   * Build CSS dengan optimasi production
   */
  async buildCSS() {
    return new Promise((resolve, reject) => {
      console.log("🎨 Building optimized CSS...");

      const args = [
        "-i",
        "./src/css/input.css",
        "-o",
        "./dist/css/output.css",
        "--config",
        "./config/tailwind.config.js",
        "--minify",
      ];

      const tailwindProcess = spawn("npx", ["tailwindcss", ...args], {
        stdio: "pipe",
        shell: true,
      });

      tailwindProcess.stdout.on("data", (data) => {
        console.log(`TailwindCSS: ${data}`);
      });

      tailwindProcess.stderr.on("data", (data) => {
        console.error(`TailwindCSS Error: ${data}`);
      });

      tailwindProcess.on("close", (code) => {
        if (code === 0) {
          // Get CSS file size
          const cssPath = "./dist/css/output.css";
          if (fs.existsSync(cssPath)) {
            this.buildStats.cssSize = fs.statSync(cssPath).size;
          }

          console.log("✅ CSS built and minified successfully!");
          resolve();
        } else {
          reject(new Error(`TailwindCSS process exited with code ${code}`));
        }
      });
    });
  }

  /**
   * Build dan minify JavaScript
   */
  async buildJS() {
    console.log("📦 Building JavaScript...");

    const srcJsPath = "./src/js";
    const distJsPath = "./dist/js";

    if (fs.existsSync(srcJsPath)) {
      // Copy JS files (untuk saat ini, nanti bisa ditambah bundling)
      this.copyDirectory(srcJsPath, distJsPath);

      // Get total JS size
      this.buildStats.jsSize = this.getDirectorySize(distJsPath);

      console.log("✅ JavaScript files copied");
    } else {
      console.log("⚠️  No JavaScript source files found");
    }
  }

  /**
   * Optimize HTML
   */
  async optimizeHTML() {
    console.log("📄 Optimizing HTML...");

    const htmlPath = "./index.html";
    if (fs.existsSync(htmlPath)) {
      let htmlContent = fs.readFileSync(htmlPath, "utf8");

      // Basic HTML optimization (remove comments, extra whitespace)
      if (prodConfig.build.minify) {
        htmlContent = htmlContent
          .replace(/<!--[\s\S]*?-->/g, "") // Remove comments
          .replace(/\s+/g, " ") // Collapse whitespace
          .replace(/>\s+</g, "><"); // Remove whitespace between tags
      }

      // Copy optimized HTML to dist
      fs.writeFileSync("./dist/index.html", htmlContent);
      this.buildStats.htmlSize = fs.statSync("./dist/index.html").size;

      console.log("✅ HTML optimized");
    }
  }

  /**
   * Copy assets ke dist
   */
  async copyAssets() {
    console.log("📁 Copying assets...");

    const assetsPath = "./assets";
    const distAssetsPath = "./dist/assets";

    if (fs.existsSync(assetsPath)) {
      this.copyDirectory(assetsPath, distAssetsPath);
      this.buildStats.totalAssets = this.countFiles(distAssetsPath);
      console.log(`✅ ${this.buildStats.totalAssets} assets copied`);
    } else {
      console.log("⚠️  No assets directory found");
    }
  }

  /**
   * Copy directory recursively
   */
  copyDirectory(src, dest) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }

    const entries = fs.readdirSync(src, { withFileTypes: true });

    for (const entry of entries) {
      const srcPath = path.join(src, entry.name);
      const destPath = path.join(dest, entry.name);

      if (entry.isDirectory()) {
        this.copyDirectory(srcPath, destPath);
      } else {
        fs.copyFileSync(srcPath, destPath);
      }
    }
  }

  /**
   * Get directory size in bytes
   */
  getDirectorySize(dirPath) {
    let totalSize = 0;

    if (fs.existsSync(dirPath)) {
      const files = fs.readdirSync(dirPath, { withFileTypes: true });

      for (const file of files) {
        const filePath = path.join(dirPath, file.name);

        if (file.isDirectory()) {
          totalSize += this.getDirectorySize(filePath);
        } else {
          totalSize += fs.statSync(filePath).size;
        }
      }
    }

    return totalSize;
  }

  /**
   * Count files in directory
   */
  countFiles(dirPath) {
    let count = 0;

    if (fs.existsSync(dirPath)) {
      const files = fs.readdirSync(dirPath, { withFileTypes: true });

      for (const file of files) {
        const filePath = path.join(dirPath, file.name);

        if (file.isDirectory()) {
          count += this.countFiles(filePath);
        } else {
          count++;
        }
      }
    }

    return count;
  }

  /**
   * Format bytes ke human readable
   */
  formatBytes(bytes) {
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  /**
   * Generate build report
   */
  generateBuildReport() {
    const buildTime = Date.now() - this.buildStartTime;
    const totalSize =
      this.buildStats.cssSize +
      this.buildStats.jsSize +
      this.buildStats.htmlSize;

    console.log("\n📊 Build Report:");
    console.log("================");
    console.log(`⏱️  Build time: ${buildTime}ms`);
    console.log(`📄 HTML size: ${this.formatBytes(this.buildStats.htmlSize)}`);
    console.log(`🎨 CSS size: ${this.formatBytes(this.buildStats.cssSize)}`);
    console.log(`📦 JS size: ${this.formatBytes(this.buildStats.jsSize)}`);
    console.log(`📁 Total assets: ${this.buildStats.totalAssets} files`);
    console.log(`📊 Total bundle size: ${this.formatBytes(totalSize)}`);
    console.log("================\n");
  }
}

// Start production build
const builder = new ProductionBuilder();
builder.build().catch((error) => {
  console.error("❌ Build failed:", error);
  process.exit(1);
});
