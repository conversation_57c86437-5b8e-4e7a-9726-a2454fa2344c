/**
 * Development Build Script
 * Script untuk development dengan hot reload dan watch mode
 */

const fs = require("fs");
const path = require("path");
const { spawn } = require("child_process");

// Import environment configuration
const devConfig = require("../config/environments/development.js");

class DevServer {
  constructor() {
    this.isRunning = false;
    this.watchers = [];
    this.processes = [];
  }

  /**
   * Start development server
   */
  async start() {
    console.log("🚀 Starting development server...");
    console.log(`📍 Environment: ${devConfig.NODE_ENV}`);
    console.log(
      `🌐 Server: http://${devConfig.server.host}:${devConfig.server.port}`
    );

    try {
      // Build CSS untuk pertama kali
      await this.buildCSS();

      // Start file watchers
      this.startWatchers();

      // Start local server jika diperlukan
      if (devConfig.server.open) {
        this.startLocalServer();
      }

      this.isRunning = true;
      console.log("✅ Development server started successfully!");
      console.log("👀 Watching for file changes...");
    } catch (error) {
      console.error("❌ Failed to start development server:", error);
      process.exit(1);
    }
  }

  /**
   * Build CSS menggunakan TailwindCSS CLI
   */
  async buildCSS() {
    return new Promise((resolve, reject) => {
      console.log("🎨 Building CSS...");

      const args = [
        "-i",
        "./src/css/input.css",
        "-o",
        "./dist/css/output.css",
        "--config",
        "./config/tailwind.config.js",
      ];

      if (devConfig.build.watch) {
        args.push("--watch");
      }

      const tailwindProcess = spawn("npx", ["tailwindcss", ...args], {
        stdio: "pipe",
        shell: true,
      });

      tailwindProcess.stdout.on("data", (data) => {
        if (devConfig.debug.verbose) {
          console.log(`TailwindCSS: ${data}`);
        }
      });

      tailwindProcess.stderr.on("data", (data) => {
        console.error(`TailwindCSS Error: ${data}`);
      });

      tailwindProcess.on("close", (code) => {
        if (code === 0) {
          console.log("✅ CSS built successfully!");
          resolve();
        } else {
          reject(new Error(`TailwindCSS process exited with code ${code}`));
        }
      });

      // Simpan process untuk cleanup nanti
      this.processes.push(tailwindProcess);
    });
  }

  /**
   * Start file watchers untuk auto-reload
   */
  startWatchers() {
    const watchPaths = ["./src/js", "./index.html", "./assets"];

    watchPaths.forEach((watchPath) => {
      if (fs.existsSync(watchPath)) {
        console.log(`👀 Watching: ${watchPath}`);

        const watcher = fs.watch(
          watchPath,
          { recursive: true },
          (eventType, filename) => {
            if (filename && devConfig.debug.enabled) {
              console.log(`📝 File changed: ${filename} (${eventType})`);

              // Reload browser jika diperlukan
              if (devConfig.server.hot) {
                this.reloadBrowser();
              }
            }
          }
        );

        this.watchers.push(watcher);
      }
    });
  }

  /**
   * Start simple local server
   */
  startLocalServer() {
    const http = require("http");
    const url = require("url");
    const path = require("path");

    const server = http.createServer((req, res) => {
      const parsedUrl = url.parse(req.url);
      let pathname = `.${parsedUrl.pathname}`;

      // Default ke index.html
      if (pathname === "./") {
        pathname = "./index.html";
      }

      // Serve static files
      fs.readFile(pathname, (err, data) => {
        if (err) {
          res.statusCode = 404;
          res.end("File not found");
          return;
        }

        // Set content type
        const ext = path.extname(pathname);
        const contentTypes = {
          ".html": "text/html",
          ".css": "text/css",
          ".js": "text/javascript",
          ".png": "image/png",
          ".jpg": "image/jpeg",
          ".svg": "image/svg+xml",
        };

        res.setHeader("Content-Type", contentTypes[ext] || "text/plain");
        res.statusCode = 200;
        res.end(data);
      });
    });

    server.listen(devConfig.server.port, devConfig.server.host, () => {
      console.log(
        `🌐 Local server running at http://${devConfig.server.host}:${devConfig.server.port}`
      );
    });
  }

  /**
   * Reload browser (placeholder untuk future implementation)
   */
  reloadBrowser() {
    if (devConfig.debug.verbose) {
      console.log("🔄 Browser reload triggered");
    }
  }

  /**
   * Stop development server
   */
  stop() {
    console.log("🛑 Stopping development server...");

    // Close watchers
    this.watchers.forEach((watcher) => watcher.close());

    // Kill processes
    this.processes.forEach((process) => {
      if (!process.killed) {
        process.kill();
      }
    });

    this.isRunning = false;
    console.log("✅ Development server stopped");
  }
}

// Handle process termination
process.on("SIGINT", () => {
  console.log("\n🛑 Received SIGINT, shutting down gracefully...");
  if (devServer.isRunning) {
    devServer.stop();
  }
  process.exit(0);
});

process.on("SIGTERM", () => {
  console.log("\n🛑 Received SIGTERM, shutting down gracefully...");
  if (devServer.isRunning) {
    devServer.stop();
  }
  process.exit(0);
});

// Start development server
const devServer = new DevServer();
devServer.start().catch((error) => {
  console.error("❌ Development server failed:", error);
  process.exit(1);
});
