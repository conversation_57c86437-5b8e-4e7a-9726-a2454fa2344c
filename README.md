# Personal Portfolio - <PERSON>

## 📋 Deskripsi Project
Situs web Personal Portfolio untuk Muhammad S<PERSON><PERSON><PERSON> yang dibangun dengan teknologi modern dan mengikuti best practices dalam web development.

## 🚀 Teknologi yang Digunakan
- **HTML5** - Semantic markup dan accessibility
- **TailwindCSS v4.1.11** - Modern CSS framework
- **Vanilla JavaScript** - ES6+ modular JavaScript
- **TailwindCSS CLI** - Build system

## 🏗️ Struktur Project
```
personal-portfolio/
├── assets/                     # Asset statis
│   ├── icons/                  # Icon dan SVG
│   │   └── social-media/       # Icon media sosial
│   ├── images/                 # Gambar dan foto
│   │   ├── certificates/       # Gambar sertifikat
│   │   ├── profile/           # Foto profil
│   │   ├── projects/          # Screenshot project
│   │   └── tech-stacks/       # Logo teknologi
│   ├── fonts/                 # Custom fonts
│   └── videos/                # Video assets
├── config/                    # Konfigurasi
│   ├── environments/          # Environment configs
│   └── tailwind.config.js     # TailwindCSS config
├── src/                       # Source code
│   ├── css/                   # CSS source
│   │   └── input.css          # Main CSS input
│   ├── scss/                  # SCSS structure
│   │   ├── base/              # Base styles
│   │   ├── components/        # Component styles
│   │   ├── layouts/           # Layout styles
│   │   └── utilities/         # Utility styles
│   └── js/                    # JavaScript source
│       ├── components/        # Reusable components
│       ├── modules/           # Feature modules
│       ├── services/          # API services
│       └── utils/             # Utility functions
├── dist/                      # Build output
│   ├── css/                   # Compiled CSS
│   └── js/                    # Compiled JavaScript
├── tools/                     # Build tools
│   ├── build.js              # Production build
│   └── dev.js                # Development server
├── docs/                      # Dokumentasi
│   ├── api/                   # API documentation
│   └── setup/                 # Setup guides
├── public/                    # Public assets
│   └── manifest/              # PWA manifest
└── index.html                 # Entry point
```

## 🎯 Fitur Utama
- ✅ Responsive design (Mobile, Tablet, Desktop)
- ✅ Dark/Light mode toggle
- ✅ Smooth scrolling navigation
- ✅ Security features (anti right-click, inspect protection)
- ✅ Performance optimized
- ✅ SEO friendly
- ✅ Accessibility compliant
- ✅ Modern animations dan transitions

## 🛡️ Security Features
- Anti right-click protection
- Developer tools detection
- Copy-paste protection
- Content protection
- Secure navigation

## 📱 Responsive Breakpoints
- Mobile: 320px - 768px
- Tablet: 768px - 1024px
- Desktop: 1024px+

## 🎨 Design System
- **Colors**: #181818 (Dark), #FFFFFF (Light)
- **Fonts**: Merriweather (Headings), Open Sans (Body)
- **Design Principles**: Symmetric, Precise, Aesthetic, Minimalist

## 🚀 Development
```bash
# Install dependencies
npm install

# Development mode
npm run dev

# Build for production
npm run build
```

## 📄 License
© 2025 Muhammad Syaamil Muzhaffar. All rights reserved.
